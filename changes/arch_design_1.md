# 音频剧本生成优化架构设计（现实版强规则驱动 - 基于实际数据与生产实践优化）

## 执行摘要

本文档基于对实际数据文件（如`save_witch_whole.json`）的分析，并深度融合了当前基于LLM的播客脚本生产最佳实践与审慎评估，提出了一个**高度现实、健壮且具备自我修复能力的强规则驱动音频剧本优化架构**。

**核心原则：通过精密的输入预处理、严格的LLM输出规约、规则引擎主导的验证与初步处理、以及创新的"LLM自我修复循环"和"嵌入式声音风格"，最大限度地自动化高质量音频剧本的生成，同时严格控制成本与确保系统稳定性。**

目标是不仅解决音频节目"谁在说话"、"情节是否吸引人"的核心挑战，更要确保整个系统在处理大规模、真实世界数据（如长篇小说章节摘要）时的鲁棒性、可维护性和成本效益。

## 实践驱动的架构演进基础

本架构特别考虑了以下生产实践中的关键挑战与应对策略：
- **长输入处理**：真实章节摘要可能长达数千字，远超LLM最佳处理范围。
- **输入质量不一**：原始摘要可能缺乏结构，甚至包含不连贯信息。
- **自动化瓶颈**：简单的"标记问题供人工处理"无法实现真正的低成本自动化。
- **角色声音一致性**：手动维护大量角色的语言特征库不具备扩展性。
- **音频独有节奏**：单纯的字数统计无法保证听觉上的良好节奏。
- **成本控制的现实性**：必须考虑LLM调用失败、内容策略限制及必要的重试成本。

## 核心理念：预处理 -> LLM结构化 (JSON) -> 规则验证 -> 自我修复 -> LLM润色 -> 最终校验

### 1. 输入预处理与浓缩 (Input Pre-conditioning & Enrichment)
   - **目标**：将原始、冗长的输入（如章节摘要）转化为LLM更易处理、信息更聚焦的短文本块，并补充关键的结构化信息（冲突、情感、角色），为后续高质量场景生成奠定基础。
   - **手段**：自动分块、基于规则或轻量级LLM提取核心冲突（"谁、想要什么、为什么、对抗什么"）、情感基调、关键转折点，并生成滚动更新的当前块内角色列表。

### 2. LLM场景生成与严格JSON输出 (LLM Scene Generation with Strict JSON Output)
   - **目标**：利用LLM将预处理后的摘要块转化为结构化的、机器可读的场景数据。严格的JSON Schema是后续所有自动化规则处理和自我修复的前提。
   - **手段**：要求LLM输出严格遵守预定义的JSON Schema，包含场景ID、地点、节拍摘要、对话列表（含说话人）、旁白、场景内角色等。

### 3. 规则引擎主导的解析、验证与节奏控制 (Rule-Engine Lead for Parsing, Validation & Pacing)
   - **目标**：对LLM生成的JSON进行快速、低成本的确定性处理。
   - **手段**：JSON直接解析，格式校验，初步的角色和事件一致性检查，关键的音频节奏指标检查（如平均句长、预估场景时长、动作密度）。

### 4. LLM自我修复循环 (LLM Self-Repair Loop)
   - **目标**：对规则引擎发现的多数问题进行自动化修正，减少人工干预。
   - **手段**：将验证错误通过特定的"差异化提示（Diff Prompt）"反馈给LLM，要求其仅修正问题部分，并重新提交验证。设有重试上限。

### 5. LLM创意增强与嵌入式声音风格 (LLM Creative Enhancement with Embedded Voice Style)
   - **目标**：在结构正确的基础上，提升剧本的艺术性和听感，并赋予角色独特声音。
   - **手段**：LLM优化对话自然度、情感表达，并参考通过文本嵌入（如CLIP或E5模型）生成的角色声音风格向量进行创作。

### 6. 最终规则校验与打包 (Final Rule Validation & Packaging)
   - **目标**：确保输出成品符合所有规范。
   - **手段**：对增强后的剧本进行最终的格式、一致性和量化指标检查。

## 工作流程（基于生产实践优化版）

**整体流程图示概念:**
`输入预处理 -> [针对每个摘要块: LLM场景生成(JSON) -> 规则解析与校验 -> (若有问题) -> LLM自我修复 -> 回到规则校验] -> (若通过) -> LLM创意增强 -> 最终规则校验 -> 输出`

**阶段详述:**

- **阶段 A: 输入预处理与浓缩 (0-1次LLM调用/原始章节摘要 + 微小调用/块)**
    - 输入: 原始章节摘要 (如 `group_summary`，可能长达2000-3000字)。
    - 任务:
        1.  **自动分块**: 使用滑动窗口或基于自然断点（如空行、特定分隔符）将长摘要切分为多个更小的、LLM友好的文本块（例如，目标800 Token以内）。**为增强上下文连续性，可引入约20%的块间重叠（overlap）。**
        2.  **信息浓缩与结构化**: （可选，可基于规则或1次LLM调用/原始摘要）为原始摘要或每个块添加关键标签：
            -   `protagonist` (主角), `goal` (目标), `antagonist` (对手), `turning_point` (转折点)。
            -   `conflict_ladder` (冲突阶梯): 简述主要冲突的起因、发展、高潮。
            -   `emotions_to_hit` (需表达情感): 如 `{\"角色A\": [\"恐惧\", \"释然\"]}`。
        3.  **滚动式角色列表生成**: （微小LLM调用/块 或 规则提取）"列出此块中所有独特角色及其一句话角色描述"，追加到全局或上下文相关的角色字典。
        4.  **上下文继承**: 为保证伏笔和悬念的连贯性，**每个块在处理时应被注入其前一个块的`beat_summary`** (例如：`chunk(i).prompt = prev_beat_summary + current_chunk_text`)。
    - 输出: 经过预处理的、信息更丰富的摘要块列表，每个块附带其上下文角色列表。

- **阶段 B: LLM场景生成 (严格JSON输出) (1次LLM调用/摘要块)**
    - 输入: 阶段A输出的单个摘要块及其相关信息。
    - 任务: LLM根据摘要块内容，生成遵循预定义严格JSON Schema的场景描述。
        ```json
        {
          "scene_id": "1.2", // 场景ID
          "location": "城堡广场", // 地点
          "beat_summary": "罗兰推迟了女巫的处决。", // 本场景核心节拍/事件摘要
          "dialogue": [ // 对话列表
            {"speaker": "罗兰", "line": "今天的处决暂停..."},
            {"speaker": "民众", "line": "为什么？王子殿下！"}
          ],
          "narration": ["冬日的寒风呼啸而过，广场上的人群窃窃私语。"], // 旁白列表
          "characters_present": ["罗兰", "巴罗夫", "民众"] // 当前场景出现的角色
        }
        ```
    - 输出: 符合JSON Schema的场景数据字符串。

- **阶段 C: 规则解析、校验与节奏控制 (0次API调用, 规则引擎)**
    - 输入: 阶段B输出的JSON场景数据。
    - 任务:
        1.  **JSON解析与Schema验证**: `json.loads()` 并校验是否符合预定义Schema。
        2.  **数据提取**: 提取角色、对话、旁白等信息。记录角色首次出场。
        3.  **格式与基础一致性校验**: 如说话人是否在`characters_present`中，`scene_id`是否递增等。
        4.  **应用首次出场模板**: 若角色首次出场，应用标准介绍。
        5.  **音频节奏与时长校验**:
            -   `avg_words_per_line`: 计算所有`dialogue.line`的平均词数。
            -   `estimated_scene_duration_seconds`: `(sum(len(dialogue.line.split())) + sum(len(narration_line.split()))) / words_per_minute_target * 60` (其中`words_per_minute_target`需根据场景类型动态调整，见下表)。
            -   `action_beat_density`: 通过正则匹配旁白和动作描述中的关键词（如表示动作的动词、音效提示如`[脚步声]`, `[开门声]`）来估算每分钟的动作节拍数 (同样根据场景类型调整目标值)。
            -   **场景类型敏感的KPI阈值**: 为了避免一刀切导致不自然的节奏（例如，强行拉长短促的动作场景或静默片段），KPI应根据场景类型进行分档。需要预先定义场景类型（例如，通过LLM在阶段B生成时标注，或在阶段C根据内容特征分类）。

        | 场景类型   | 目标 WPM (词/分钟) | 目标动作/分钟 | 备注         |
        |------------|--------------------|---------------|--------------|
        | 对话为主   | 140-160            | ≥0.8          | 情绪张弛主导 |
        | 行动为主   | 120-140            | ≥1.2          | 背景音丰富   |
        | 旁白/过渡 | 150-170            | ≥0.2          | 可接受略长句 |

        6.  **其他硬性规则检查**: 如对话轮次、旁白长度等。
    - 输出: 结构化的场景对象 + 包含所有错误/警告的验证报告。

- **阶段 D: LLM自我修复循环 (0-2次LLM调用/轮, 平均约0.7次/块)**
    - 输入: 阶段C的场景对象及验证报告中的错误。
    - 任务:
        1.  若验证报告含错误，构建"差异化提示"："原始输入场景JSON如下：\n```json\n{...original_scene_json...}\n```\n规则检查发现以下问题（请使用提供的ID-Path定位并修正）：\n- [问题1描述，例如：`{"op": "replace", "path": "/dialogue/2/speaker", "current_value": "XXX", "fix_suggestion": "应为 characters_present 中的一员"}`]\n- [问题2描述，例如：`{"op": "replace", "path": "/metadata/estimated_duration_seconds", "current_value": 25, "fix_suggestion": "场景时长低于最低30秒要求"}`]\n请严格按照JSON Patch规范 (RFC 6902) 返回一个JSON对象数组，其中包含修正上述问题的操作。例如：`[{"op": "replace", "path": "/dialogue/2/speaker", "value": "罗兰"}]`。如果无需修改，请返回空数组 `[]`。"
        2.  LLM尝试修正并返回新的JSON。
        3.  **返回阶段C重新验证**。
        4.  **重试上限**: 最多尝试2次自我修复。若仍有问题，则标记该场景需人工审核。
    - 输出: 修正后的JSON场景数据 (若成功) 或 标记为"需人工审核"的场景。

- **阶段 E: LLM创意增强与嵌入式声音风格 (1次LLM调用/块或场景)**
    - 输入: 从阶段C/D循环验证通过的、结构正确的场景JSON。
    - 任务:
        1.  **上下文感知润色**:
            -   向LLM提供当前场景的完整JSON数据（已通过验证和修复）。
            -   附带预处理阶段提取的`conflict_ladder`, `emotions_to_hit`等作为创作指导。
        2.  **声音风格融入 (Few-Shot为主，向量为辅)**:
            -   **主要方法：Few-Shot黄金台词**: 
                -   为每个主要角色人工挑选或由LLM辅助生成2-3条最能代表其核心性格和语言模式的"黄金台词"。
                -   在Prompt中向LLM提供这些示例台词，并指示其模仿该角色的口吻和风格。例如：
                    ```
                    以下是[角色名]的一些代表性台词：
                    - "[黄金台词1]"
                    - "[黄金台词2]"
                    请为[角色名]创作以下对话，保持其类似的口吻和核心性格特点：'[原始生成对话]'
                    同时请让对话更自然/更符合当前[紧张/轻松]的氛围。
                    ```
            -   **辅助与备选方案：文本嵌入向量**: 
                -   (离线准备) 使用SBERT、E5等文本嵌入模型将每个主要角色的"黄金台词"（或更丰富的代表性语料）编码为固定维度的向量，并存储。
                -   (在线应用，可选) 在Prompt中加入指令，要求LLM生成的对话在语义上贴近目标角色的风格向量。此方案的有效性需结合具体模型进行A/B测试验证听感。
                -   主要挑战：多数商用LLM对直接在prompt中理解高维向量的语义支持尚不明确，few-shot通常更稳健。
        3.  **确保情节与情感表达**: 确保预处理阶段（阶段A）提取的`conflict_ladder`和`emotions_to_hit`得到体现。
        4.  保持JSON Schema输出。
    - 输出: 经过创意增强和风格化处理的场景JSON。

- **阶段 F: 最终规则校验与打包 (0次API调用, 规则引擎)**
    - 输入: 阶段E输出的增强版场景JSON。
    - 任务:
        1.  最终全面的Schema符合性、格式规范性、命名一致性校验。
        2.  最终的量化指标检查（时长、节奏等）。
        3.  整合所有场景，生成完整剧本。
        4.  若仍有无法自动修复的硬性错误，标记供人工审核（理论上此阶段错误应极少）。
    - 输出: 最终待发布的音频剧本（或其结构化表示），附带最终验证报告。

**总计API调用估算**:
-   **预处理阶段A (可选LLM部分)**: 0-1次 LLM调用 / 原始章节摘要。
-   **核心处理 (阶段B+D+E) / 每个约800 Token的摘要块**:
    -   阶段B (场景生成): 1次
    -   阶段D (自我修复): 平均0.5 - 1次 (假设多数情况1次修复成功，部分无需修复，少数2次)
    -   阶段E (创意增强): 1次
    -   **小计: 约2.5 - 3次 LLM调用 / 摘要块**。
-   这个估算更细致地考虑了实际操作中的分块和修复流程。一个原始章节摘要可能被分为多个块进行处理。

### 详细对比表 (与上一版"现实版"对比)
| 维度 | 上一版"现实版"架构 | 本版"生产实践优化版" | 改进方向与理由 |
|------|--------------------|----------------------|----------------|
| **核心流程** | 线性五阶段 (P0-P4) | 包含预处理和自我修复循环的六阶段 (PA-PF) | 增强鲁棒性，提高自动化率 |
| **输入处理** | LLM直接处理完整group_summary | 预处理分块+信息浓缩 | 提升LLM处理超长输入效果，提供更佳上下文 |
| **LLM输出规约** | 初步剧本草稿 | 严格的JSON Schema | 保证后续规则处理的确定性和简易性 |
| **问题处理** | Phase 4标记问题，不自动修正 | Phase D引入LLM自我修复循环 | 大幅减少人工干预，实现"廉价自动化" |
| **角色声音** | 结构化语言特征库（手动） | 嵌入式声音风格（向量化） | 提升可扩展性和维护性，动态适应新角色 |
| **节奏控制** | 基础字数检查 | 更细致的音频节奏指标（WPM, 动作密度等） | 更贴近真实听感 |
| **API调用估算** | 3-5次/group (较宏观) | 2.5-3次/约800Token块 + 可选预处理调用 (更细致) | 估算更准确，明确了分块处理策略 |
| **成本控制** | 提及，但未深入重试策略 | 明确重试上限，预检Token，敏感内容处理 | 更全面地考虑实际成本因素 |
| **风险缓解** | 较通用 | 针对性缓解长输入、内容策略、修复失败等风险 | 更具操作性的风险管理 |

## 现实版强规则驱动音频剧本优化架构设计（生产实践优化版）

### A. 输入预处理与浓缩

#### 目标与价值
原始章节摘要往往冗长、缺乏结构，直接输入LLM效果不佳。此阶段通过自动化手段，将其转化为LLM更易处理、信息更聚焦的短文本块，并补充关键的结构化信息（冲突、情感、角色），为后续高质量场景生成奠定基础。

#### 技术实现
1.  **智能文本分块 (Smart Chunking)**:
    -   优先基于自然段落、特定分隔符（如 "###", "\*\*\*") 进行切分。
    -   若无明显分隔，则采用滑动窗口，确保每个块在目标Token数（如800）以内，并尽可能保持语义完整性（如句子不被切断）。**为增强上下文连续性，可引入约20%的块间重叠（overlap）。**
2.  **关键信息提取与标注 (Key Information Extraction & Tagging)**:
    -   **规则与关键字**: 使用正则表达式和关键字列表初步识别主角、目标、主要对手、关键事件。
    -   **轻量级LLM辅助**: (可选，针对复杂摘要) 设计一个非常简短的Prompt，要求LLM从原始摘要或块中提取上述结构化标签信息 (`conflict_ladder`, `emotions_to_hit`)。
    -   **滚动式角色列表**: 对于每个块，通过简单规则或极短提示（"列出此块中所有唯一出现的人名及其一句话简介"）维护一个当前块相关的角色清单及其简要描述。这有助于LLM在生成当前块时保持角色认知。
    -   **上下文继承**: 为保证伏笔和悬念的连贯性，**每个块在处理时应被注入其前一个块的`beat_summary`** (例如：`chunk(i).prompt = prev_beat_summary + current_chunk_text`)。

### B. LLM场景生成 (严格JSON输出)

#### 目标与价值
将预处理后的摘要块转化为结构化的、机器可读的场景数据。严格的JSON Schema是后续所有自动化规则处理和自我修复的前提。

#### 技术实现
1.  **精心设计的Prompt**:
    -   明确指示LLM根据提供的摘要块（及附带的浓缩信息）生成场景。
    -   **强制输出JSON格式**: "你的输出必须是一个严格符合以下JSON Schema的字符串: [在此处粘贴JSON Schema定义]"。
    -   提供清晰的字段说明和示例。
    -   利用LLM的Function Calling能力（如果可用）来更好地保证JSON输出的合规性。
2.  **JSON Schema 定义**: (如前文示例) 包含 `scene_id`, `location`, `beat_summary`, `dialogue` (列表，每个元素含 `speaker`, `line`), `narration` (列表), `characters_present` (列表)。

### C. 规则解析、校验与节奏控制

#### 目标与价值
对LLM生成的JSON进行快速、确定性的验证，确保其结构正确性、内容基础一致性，并初步评估其是否符合音频媒介的节奏要求。

#### 技术实现
1.  **JSON解析与验证**: 标准库`json.loads()`，然后用JSON Schema校验库（如`jsonschema` for Python）进行严格校验。
2.  **内容提取与基础校验**:
    -   遍历JSON结构，提取各字段数据存入内部对象。
    -   检查`dialogue`中的`speaker`是否均在`characters_present`列表中。
    -   检查角色首次出场情况，并应用预设的介绍模板（例如，旁白中加入"这是罗兰王子第一次出现在我们面前，他看起来……"）。
3.  **音频节奏校验模块**:
    -   `avg_words_per_line`: 计算所有`dialogue.line`的平均词数。
    -   `estimated_scene_duration_seconds`: `(sum(len(dialogue.line.split())) + sum(len(narration_line.split()))) / words_per_minute_target * 60` (其中`words_per_minute_target`需根据场景类型动态调整，见下表)。
    -   `action_beat_density`: 通过正则匹配旁白和动作描述中的关键词（如表示动作的动词、音效提示如`[脚步声]`, `[开门声]`）来估算每分钟的动作节拍数 (同样根据场景类型调整目标值)。
    -   **场景类型敏感的KPI阈值**: 为了避免一刀切导致不自然的节奏（例如，强行拉长短促的动作场景或静默片段），KPI应根据场景类型进行分档。需要预先定义场景类型（例如，通过LLM在阶段B生成时标注，或在阶段C根据内容特征分类）。

        | 场景类型   | 目标 WPM (词/分钟) | 目标动作/分钟 | 备注         |
        |------------|--------------------|---------------|--------------|
        | 对话为主   | 140-160            | ≥0.8          | 情绪张弛主导 |
        | 行动为主   | 120-140            | ≥1.2          | 背景音丰富   |
        | 旁白/过渡 | 150-170            | ≥0.2          | 可接受略长句 |

4.  **生成验证报告**: 将所有检查结果（成功、警告、错误）汇总成结构化报告。

### D. LLM自我修复循环

#### 目标与价值
核心创新点。赋予系统自我纠错能力，能自动修复多数由规则引擎发现的、相对明确的问题，从而大幅减少人工审核和修改的负担。

#### 技术实现
1.  **差异化提示构建 (Diff Prompt Construction)**:
    -   当验证报告包含错误时，动态构建提示。
    -   提示结构调整为："原始输入场景JSON如下：\n```json\n{...original_scene_json...}\n```\n规则检查发现以下问题（请使用提供的ID-Path定位并修正）：\n- [问题1描述，例如：`{"op": "replace", "path": "/dialogue/2/speaker", "current_value": "XXX", "fix_suggestion": "应为 characters_present 中的一员"}`]\n- [问题2描述，例如：`{"op": "replace", "path": "/metadata/estimated_duration_seconds", "current_value": 25, "fix_suggestion": "场景时长低于最低30秒要求"}`]\n请严格按照JSON Patch规范 (RFC 6902) 返回一个JSON对象数组，其中包含修正上述问题的操作。例如：`[{"op": "replace", "path": "/dialogue/2/speaker", "value": "罗兰"}]`。如果无需修改，请返回空数组 `[]`。"
2.  **LLM调用与重试**: 
    -   将构建好的提示发送给LLM。
    -   **对LLM返回的JSON Patch在本地应用到原始JSON上**。如果应用失败或返回格式不正确，视为一次修复失败。
    -   对应用patch后的JSON再次送入阶段C进行验证。
    -   设置重试上限（如2次）。若达到上限仍未解决所有硬性错误，则该场景被标记为"需要人工干预"。
3.  **Token优化**: 自我修复的提示因仅包含错误路径和修正建议，且LLM仅需返回轻量的JSON Patch，**显著减少了Token消耗**，降低了因输入输出过长导致的截断风险，提升了修复效率和成功率，通常一到两轮即可收敛。

### E. LLM创意增强与嵌入式声音风格

#### 目标与价值
在确保剧本结构和基础规范性的前提下，利用LLM进行艺术加工，提升对话的生动性、情感的饱满度，并赋予角色独特的、可识别的声音特质。

#### 技术实现
1.  **上下文感知润色**:
    -   向LLM提供当前场景的完整JSON数据（已通过验证和修复）。
    -   附带预处理阶段提取的`conflict_ladder`, `emotions_to_hit`等作为创作指导。
2.  **声音风格融入 (Few-Shot为主，向量为辅)**:
    -   **主要方法：Few-Shot黄金台词**: 
        -   为每个主要角色人工挑选或由LLM辅助生成2-3条最能代表其核心性格和语言模式的"黄金台词"。
        -   在Prompt中向LLM提供这些示例台词，并指示其模仿该角色的口吻和风格。例如：
            ```
            以下是[角色名]的一些代表性台词：
            - "[黄金台词1]"
            - "[黄金台词2]"
            请为[角色名]创作以下对话，保持其类似的口吻和核心性格特点：'[原始生成对话]'
            同时请让对话更自然/更符合当前[紧张/轻松]的氛围。
            ```
    -   **辅助与备选方案：文本嵌入向量**: 
        -   (离线准备) 使用SBERT、E5等文本嵌入模型将每个主要角色的"黄金台词"（或更丰富的代表性语料）编码为固定维度的向量，并存储。
        -   (在线应用，可选) 在Prompt中加入指令，要求LLM生成的对话在语义上贴近目标角色的风格向量。此方案的有效性需结合具体模型进行A/B测试验证听感。
        -   主要挑战：多数商用LLM对直接在prompt中理解高维向量的语义支持尚不明确，few-shot通常更稳健。
3.  **确保情节与情感表达**: 确保预处理阶段（阶段A）提取的`conflict_ladder`和`emotions_to_hit`得到体现。
4.  **保持JSON输出**: 依然要求LLM返回符合原Schema的JSON数据。

### F. 最终规则校验与打包

#### 目标与价值
作为最后一道防线，确保所有输出的剧本片段都符合最终的质量标准和技术规范，然后将其组装成完整的剧本。

#### 技术实现
1.  **全面最终校验**: 重复阶段C中的所有校验规则，确保经过创意增强后未引入新的结构性或格式性问题。
2.  **全局一致性检查**: (如果适用) 检查跨场景的角色称呼、关键情节元素的一致性。
3.  **剧本组装与格式化**: 将所有通过校验的场景JSON数据按`scene_id`排序，整合成最终的剧本文件（如Markdown, Fountain或自定义XML/JSON）。
4.  **生成交付报告**: 包含剧本元数据、API调用统计、任何被标记为"需人工审核"的场景列表等。

## 技术组件调整与新增

- **新增：输入预处理器 (Input Preprocessor)**: 负责文本分块、信息浓缩与标签化。
- **新增：JSON Schema校验器 (JSON Schema Validator)**: 嵌入规则引擎，用于验证LLM输出。
- **新增：自我修复提示生成器 (Self-Repair Prompt Generator)**: 根据验证错误动态构建给LLM的修复指令。
- **调整：LLM场景生成器**: 强调输出严格的JSON。
- **新增：声音风格嵌入管理器 (Voice Style Embedding Manager)**: 负责存储、检索角色声音嵌入向量，并辅助构建相关Prompt。
- **调整：规则验证器**: 扩展其功能，包含更详细的音频节奏检查和各类硬性规则。

## 成本、延迟与风险控制策略

- **Token长度预检**: 在调用LLM前，估算Prompt的Token长度。若超过模型上下文限制，则进一步拆分输入或采用具备更大上下文窗口的模型（可能成本更高）。
- **敏感内容处理**: (针对可能触发内容策略的场景)
    -   预处理：在发送给LLM前，通过规则或模型识别并移除/替换潜在的敏感描述（如过度暴力）。
    -   后处理：若有移除，在LLM返回内容后，根据上下文尝试安全地重新注入或改写这部分内容（可能需要额外的人工审核）。
- **API调用重试与熔断**:
    -   针对网络波动或偶发性LLM错误，可设置1-2次简单重试。
    -   自我修复循环（阶段D）内置重试上限（如2次），超出则放弃自动修复，转人工。
    -   对整体流程或单个摘要块的总LLM调用次数也可设置上限，防止无限循环或成本失控。
- **API调用预算表**: API调用次数估算（如2.5-3次/块）应被视为"平均最佳情况"。需在预算中考虑可选的预处理LLM调用、自我修复的多次尝试、以及因内容策略或长Token处理而可能产生的额外调用。**建议为真实日均Token消耗预算设置约1.3倍的缓冲。**

### 可观测性与监控
为了确保系统的稳定运行和持续优化，必须建立完善的监控机制。关键指标应暴露给监控系统（如Prometheus）并可视化在仪表盘上：
-   **块处理成功/失败率**: `audio_pipeline_block_processed_total{status="success|failure|manual_review"}`
-   **场景生成与修复**: 
    -   `audio_pipeline_scene_generation_total{status="success|failure"}`
    -   `audio_pipeline_repair_attempts_total` (总修复尝试次数)
    -   `audio_pipeline_repair_rounds_histogram` (修复轮次分布)
    -   `audio_pipeline_repair_successful_total` (成功修复总数)
-   **内容质量指标**: 
    -   `audio_pipeline_scene_duration_seconds_histogram` (场景时长分布)
    -   `audio_pipeline_character_consistency_issues_total` (角色一致性问题计数，例如通过diff分析)
    -   `audio_pipeline_style_alignment_score_histogram` (如果可量化风格对齐度)
-   **Token消耗**: 
    -   `audio_pipeline_llm_tokens_consumed_total{stage="preprocessing|generation|repair|enhancement"}`
-   **API调用延迟与错误**: 
    -   `audio_pipeline_llm_api_latency_seconds_histogram{call_type="..."}`
    -   `audio_pipeline_llm_api_errors_total{error_type="429|policy_block|other"}`

这些指标有助于及时发现问题（如某一阶段失败率升高、修复轮次过多、成本超预期），从而调整参数、优化Prompt或在必要时降级特定功能。

### 降级与容错策略
为保证在极端情况下（如LLM服务长时间不可用或关键组件故障）仍能提供基础服务，应设计降级预案：
-   **LLM不可用时的基本模式**: 系统可降级至一个"规则驱动的极简版"流程。
    1.  **输入预处理与浓缩 (阶段A)**: 正常执行分块。
    2.  **模板化场景生成**: 跳过LLM场景生成 (阶段B) 和创意增强 (阶段E)。基于浓缩信息和角色列表，使用预设模板生成极简场景结构，例如：
        -   旁白：根据`beat_summary`或关键情节生成。
        -   对话：仅保留角色名作为占位符，或使用非常简单的通用对话指示（如"[角色A与角色B对话讨论当前局势]"）。
    3.  **基础规则校验 (简化版阶段C)**: 进行最核心的格式校验。
    4.  **跳过自我修复 (阶段D)**。
    5.  **打包输出**: 生成可读但基础的脚本草稿。
-   **目标**: 确保即使在严重故障时，流水线也不会完全停摆，至少能产出可供人工后续加工的基础稿件，维持最低限度的生产能力。

## 结论

采纳了上述基于生产实践的优化建议后，本"音频剧本生成优化架构"在保持"规则优先，LLM辅助"的核心思想基础上，显著提升了系统的自动化程度、鲁棒性、可维护性和对真实世界复杂输入的处理能力。特别是**输入预处理、严格JSON契约、LLM自我修复循环、以及嵌入式声音风格**这几项关键改进，将使系统在规模化生产中表现更佳，更能实现成本效益与高质量输出的平衡。

此架构不仅是一个技术方案，更是对音频内容创作流程深度理解和工程实践经验的体现，旨在打造一个真正能落地、能规模化应用的AI音频剧本生产线。


